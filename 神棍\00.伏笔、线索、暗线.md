# 暗线

#### 模板
- **明示线索**：
- **暗中演化**：
- **设计目标**：

# 线索

1. [[邢渊]]的战术：第18章林墨趁蔡伏安吸引视线的机会观察到的。

# 道具

#### 李解身上带有的道具：
- 【**影牙**】父亲留下的匕首**为何如此锋利**
	- **作用/秘密**：已知记录真实税赋数据，揭露官府腐败
	- **来源/去向**：从李济处获得，可能成为对抗都督府的证据
	- 第5章：林墨眼中闪过一丝与其年龄不符的狠戾。从床下摸出一块鞣制好的陈年兽皮，又找出了父亲留下的、最锋利的一把匕首。

# 伏笔

# 谜团

1. **墨晶和军备有什么关系**？第3章：“……上头说了，这关系到这几年的军备……”与他接头。蔡伏安的行动，只是在为那条真正的大鱼“标记”出水面的位置。


我要起一个名字，要十分有趣的反差，比如不会打铁的裁缝，打铁本来就是铁匠干的活，所以有反差，再比如不会看家的豪猪不是好宠物，豪猪一身刺怎么能做宠物呢？所以有反差。你可以参考：​​不会唱歌的圣经不是好乐器、不会看家的豪猪不是好宠物、不会算卦的道士不是好神棍。
请帮我设计出有反差的有趣的名字，格式：不会（技能/两个汉字）的（身份/两个汉字）不是好（名词/两-三个汉字）。要求：技能事物身份不能直接相关（比如出现了看病就不能再有医生，比如出现了织网就不能出现蜘蛛）。请帮我设计十个。

我需要设计有趣的反差对应。比如圣经对应乐器、鲨鱼对应宠物、道士对应神棍。
请问 道士 还可以对应的名词有哪些？

我需要你设计一份网文小说的脑洞设计。  
任务一：新建一个文档名为：神棍117  
任务二：我要创作一本网文，名字是：不会卜卦的道士不是好神棍。请根据这个小说名帮我设计脑洞。 基础设定：主角现代身份就是个神棍（熟读五行八卦周易等现代玄学知识，并且骗术了得，擅长扮演道士行骗）。穿越到东方玄幻仙侠的修仙架空世界，依靠神棍术生存获取修炼资源然后修道成仙。要求：金手指不能是系统流。脑洞设计要包含核心看点、核心爽点，核心创意与延伸，核心冲突设计与推演、情感冲突、主角设定（穿越前和后）以及其他关键元素。将设计追加至文档中。
请继续打开你的脑洞进行延伸和补全设计。

（书读五行八卦已经等现代玄学知识，并且骗术了得，擅长扮演道士行骗）


假“神棍”穿越真道士，在修仙世界生存是什么样的体验？
有“仙师”的身份却不会任何法术和卜算，只能靠心理学和话术夹缝求活。
看主角如何一本正经地胡说八道，用“现代玄学黑话”忽悠顶尖大能。
当谎言的雪球越滚越大，世界法则的反噬也接踵而至，只能将这场骗局进行到底。
他不仅要“欺人”，更要“欺天、欺道、欺因果”，成为一个真正能言出法随的“神棍”。
走上一条无人能理解的“欺天证道之路”。——《》

当谎言的雪球越滚越大，法则的铁律开始松动，连"天命"都变得模糊不清。
当他的骗局一次次精准地撬动现实，他才骇然发现，自己无意中窥见的，竟是这个世界早已根植于天道深处的崩溃预兆。只能将这场骗局进行到底。
当谎言的雪球越滚越大，世界法则的反噬接踵而至。
我更倾向于文档设定中的世界法则漏洞引发的危机，让主角绕开法则修补漏洞，但不能直接说出法则有漏洞剧透，也不能说天道即将崩溃。话术要引导读者认为危机是要降临到主角头上。请设计五个可以自然无缝衔接下一句的方案让我选择。



第一部修炼功法，神算相关，技能具象化。

设计更多叙事节奏与循环选择

后期当主角发现世界天道的漏洞以及世界将要毁灭的风险后，开创了自己的流派通过漏洞积蓄力量来填补这个漏洞维持世界的稳定。然而这必然触动了更巨大更深层的利益冲突。

前期被动算卦

我需要你设计一份网文小说的脑洞设计。  主要是探讨如何让读者快速代入到主角身上，切入点有哪些选择。
1. 我要创作一本网文，名字是：[不会卜卦的道士不是好神棍]。 基础设定：主角现代身份就是个神棍（熟读五行八卦周易等现代玄学知识，并且骗术了得，擅长扮演道士行骗）。穿越到东方玄幻仙侠的修仙架空世界，依靠神棍术生存获取修炼资源然后修道成仙。
2. 我的开篇构思是穿越到一个真道士身上，原主筑基初期修为，因过度使用卜算术法透支寿命活活把自己算死的，一身修为跟着神魂一起回归天地。主角穿越后开篇是没有修为的，主角穿越一睁眼，面前就是一个凶神恶煞的筑基期中期大汉强迫主角（开篇强冲突强危机）为其卜算，但主角一脸懵逼暂时还没有融合原主的记忆。根本不会算卦，靠现代神棍技术蒙混过关。信息差：大汉不知道面前的道士已经没有修为。
3. 我们只探讨如何让读者快速代入到主角身上，如何做切入点的选择。
4. 不要改动任何文档。请将完整内容，整合到一个独立的Markdown框中，让我复制粘贴。
*当必要信息缺失时，请发挥想象、大开脑洞，自行补全这些内容。当你有更高质量的提议，但与现有设定相悖时，允许对设定设当修改。* 要求：必须保证逻辑自洽。


让一件本不存在的事情实现取决于有多少人相信它的存在。